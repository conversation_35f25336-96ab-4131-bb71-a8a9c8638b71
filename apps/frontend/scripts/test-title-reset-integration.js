/**
 * 测试【矩阵系统】标题重置功能集成
 * 验证标题点击重置功能和悬停样式是否正确实现
 */

const fs = require('fs');
const path = require('path');

console.log('🎯 测试【矩阵系统】标题重置功能集成\n');

// 读取页面文件
const pageFilePath = path.join(__dirname, '../app/page.tsx');
const pageContent = fs.readFileSync(pageFilePath, 'utf8');

// 测试1: 检查标题是否添加了点击处理函数
console.log('📝 测试1: 标题点击处理函数');
const titleClickPattern = /onClick={handleTitleClick}/;
if (titleClickPattern.test(pageContent)) {
  console.log('✅ 标题已添加点击处理函数');
} else {
  console.log('❌ 标题未添加点击处理函数');
}

// 测试2: 检查是否添加了悬停样式
console.log('\n🎨 测试2: 悬停样式');
const hoverStylePattern = /hover:text-red-500/;
if (hoverStylePattern.test(pageContent)) {
  console.log('✅ 已添加悬停变红样式');
} else {
  console.log('❌ 未添加悬停变红样式');
}

// 测试3: 检查是否添加了过渡动画
console.log('\n⚡ 测试3: 过渡动画');
const transitionPattern = /transition-colors duration-200/;
if (transitionPattern.test(pageContent)) {
  console.log('✅ 已添加过渡动画效果');
} else {
  console.log('❌ 未添加过渡动画效果');
}

// 测试4: 检查是否添加了鼠标指针样式
console.log('\n👆 测试4: 鼠标指针样式');
const cursorPattern = /cursor-pointer/;
if (cursorPattern.test(pageContent)) {
  console.log('✅ 已添加鼠标指针样式');
} else {
  console.log('❌ 未添加鼠标指针样式');
}

// 测试5: 检查是否添加了提示文本
console.log('\n💡 测试5: 提示文本');
const titlePattern = /title="点击重置矩阵"/;
if (titlePattern.test(pageContent)) {
  console.log('✅ 已添加提示文本');
} else {
  console.log('❌ 未添加提示文本');
}

// 测试6: 检查重置逻辑是否正确
console.log('\n🔄 测试6: 重置逻辑');
const resetLogicPattern = /setModeConfig\('default', 'blank'\)[\s\S]*?initializeMatrix\(\)/;
if (resetLogicPattern.test(pageContent)) {
  console.log('✅ 重置逻辑正确实现');
} else {
  console.log('❌ 重置逻辑未正确实现');
}

// 测试7: 检查是否导入了必要的依赖
console.log('\n📦 测试7: 依赖导入');
const matrixStoreImportPattern = /useMatrixStore.*from.*MatrixStore/;
if (matrixStoreImportPattern.test(pageContent)) {
  console.log('✅ 已导入 useMatrixStore');
} else {
  console.log('❌ 未导入 useMatrixStore');
}

const callbackImportPattern = /useCallback.*from.*react/;
if (callbackImportPattern.test(pageContent)) {
  console.log('✅ 已导入 useCallback');
} else {
  console.log('❌ 未导入 useCallback');
}

console.log('\n🎉 标题重置功能集成测试完成！');
console.log('📋 下一步: 启动应用测试功能，确认无误后移除原重置按钮');
