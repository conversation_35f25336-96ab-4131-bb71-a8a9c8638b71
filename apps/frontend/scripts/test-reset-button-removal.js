/**
 * 测试【重置矩阵】按钮移除验证
 * 确认原重置按钮已完全移除，相关代码已清理
 */

const fs = require('fs');
const path = require('path');

console.log('🧹 测试【重置矩阵】按钮移除验证\n');

// 读取 Controls.tsx 文件
const controlsFilePath = path.join(__dirname, '../components/Controls.tsx');
const controlsContent = fs.readFileSync(controlsFilePath, 'utf8');

// 读取 page.tsx 文件
const pageFilePath = path.join(__dirname, '../app/page.tsx');
const pageContent = fs.readFileSync(pageFilePath, 'utf8');

// 测试1: 检查重置按钮是否已移除
console.log('🗑️ 测试1: 重置按钮移除');
const resetButtonPattern = /重置矩阵.*Button/s;
if (!resetButtonPattern.test(controlsContent)) {
  console.log('✅ 重置按钮已移除');
} else {
  console.log('❌ 重置按钮仍然存在');
}

// 测试2: 检查 RefreshIcon 导入是否已移除
console.log('\n📦 测试2: RefreshIcon 导入');
const refreshIconImportPattern = /import.*RefreshIcon.*from/;
if (!refreshIconImportPattern.test(controlsContent)) {
  console.log('✅ RefreshIcon 导入已移除');
} else {
  console.log('❌ RefreshIcon 导入仍然存在');
}

// 测试3: 检查 Button 导入是否已移除（如果不再使用）
console.log('\n🔘 测试3: Button 导入');
const buttonImportPattern = /import.*Button.*from/;
const buttonUsagePattern = /<Button/;
if (!buttonImportPattern.test(controlsContent) && !buttonUsagePattern.test(controlsContent)) {
  console.log('✅ Button 导入已正确移除（无使用）');
} else if (buttonImportPattern.test(controlsContent) && buttonUsagePattern.test(controlsContent)) {
  console.log('✅ Button 导入保留（仍有使用）');
} else {
  console.log('⚠️ Button 导入状态需要检查');
}

// 测试4: 检查 handleReset 函数是否已移除
console.log('\n🔄 测试4: handleReset 函数');
const handleResetPattern = /const handleReset/;
if (!handleResetPattern.test(controlsContent)) {
  console.log('✅ handleReset 函数已移除');
} else {
  console.log('❌ handleReset 函数仍然存在');
}

// 测试5: 检查 onReset 属性是否已移除
console.log('\n📝 测试5: onReset 属性');
const onResetPropPattern = /onReset\?:/;
if (!onResetPropPattern.test(controlsContent)) {
  console.log('✅ onReset 属性已从接口移除');
} else {
  console.log('❌ onReset 属性仍在接口中');
}

// 测试6: 检查页面中的 onReset 调用是否已移除
console.log('\n🔗 测试6: 页面 onReset 调用');
const pageOnResetPattern = /onReset.*=>/;
if (!pageOnResetPattern.test(pageContent)) {
  console.log('✅ 页面中的 onReset 调用已移除');
} else {
  console.log('❌ 页面中仍有 onReset 调用');
}

// 测试7: 检查 initializeMatrix 是否已从 useMatrixStore 移除
console.log('\n🏪 测试7: initializeMatrix 解构');
const initializeMatrixPattern = /initializeMatrix.*=.*useMatrixStore/s;
if (!initializeMatrixPattern.test(controlsContent)) {
  console.log('✅ initializeMatrix 已从 useMatrixStore 解构中移除');
} else {
  console.log('❌ initializeMatrix 仍在 useMatrixStore 解构中');
}

// 测试8: 检查标题重置功能是否仍然存在
console.log('\n🎯 测试8: 标题重置功能');
const titleResetPattern = /onClick={handleTitleClick}/;
if (titleResetPattern.test(pageContent)) {
  console.log('✅ 标题重置功能保持正常');
} else {
  console.log('❌ 标题重置功能丢失');
}

console.log('\n🎉 重置按钮移除验证完成！');
console.log('📋 现在重置功能完全集成到【矩阵系统】标题中');
