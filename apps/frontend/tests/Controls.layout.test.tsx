/**
 * Controls组件布局测试
 * 测试词库管理的固定标题和滚动列表功能
 */

import { render, screen } from '@testing-library/react';
import Controls from '@/components/Controls';
import { useMatrixStore } from '@/core/matrix/MatrixStore';
import { useWordLibraryStore } from '@/core/wordLibrary/WordLibraryStore';

// Mock stores
jest.mock('@/core/matrix/MatrixStore');
jest.mock('@/core/wordLibrary/WordLibraryStore');

const mockMatrixStore = {
  data: {
    selectedCells: new Set()
  },
  config: {
    mode: 'word' as const,
    mainMode: 'default' as const,
    contentMode: 'blank' as const
  },
  setModeConfig: jest.fn(),
  getDataAvailability: jest.fn(() => ({
    coordinate: true,
    color: true,
    level: true,
    word: true
  }))
};

const mockWordLibraryStore = {
  getLibrary: jest.fn(() => ({
    words: ['测试词1', '测试词2'],
    collapsed: false
  })),
  toggleLibraryCollapse: jest.fn(),
  resetAllLibraries: jest.fn(),
  exportData: jest.fn(() => '{}'),
  importData: jest.fn(() => true)
};

describe('Controls Layout', () => {
  beforeEach(() => {
    (useMatrixStore as jest.Mock).mockReturnValue(mockMatrixStore);
    (useWordLibraryStore as jest.Mock).mockReturnValue(mockWordLibraryStore);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染固定的顶部区域', () => {
    render(<Controls />);
    
    // 检查模式选择器是否存在
    const modeSelector = screen.getByRole('combobox');
    expect(modeSelector).toBeInTheDocument();
  });

  it('应该正确渲染词库管理标题', () => {
    render(<Controls />);
    
    // 检查词库管理标题是否存在
    const title = screen.getByText('词库管理');
    expect(title).toBeInTheDocument();
    
    // 检查操作按钮是否存在
    expect(screen.getByText('导出')).toBeInTheDocument();
    expect(screen.getByText('导入')).toBeInTheDocument();
    expect(screen.getByText('清空')).toBeInTheDocument();
  });

  it('应该正确渲染状态栏', () => {
    render(<Controls />);
    
    // 检查状态栏信息
    expect(screen.getByText(/模式:/)).toBeInTheDocument();
    expect(screen.getByText(/已选择:/)).toBeInTheDocument();
    expect(screen.getByText(/总计:/)).toBeInTheDocument();
  });

  it('应该有正确的CSS类结构', () => {
    const { container } = render(<Controls />);
    
    // 检查主容器
    const mainContainer = container.firstChild as HTMLElement;
    expect(mainContainer).toHaveClass('controls-container', 'h-full', 'flex', 'flex-col');
    
    // 检查固定顶部区域
    const topSection = mainContainer.children[0] as HTMLElement;
    expect(topSection).toHaveClass('flex-shrink-0', 'p-4', 'space-y-6');
    
    // 检查词库管理区域
    const wordLibrarySection = mainContainer.children[1] as HTMLElement;
    expect(wordLibrarySection).toHaveClass('flex-1', 'px-4', 'pb-4', 'min-h-0');
    
    // 检查状态栏
    const statusBar = mainContainer.children[2] as HTMLElement;
    expect(statusBar).toHaveClass('status-bar', 'flex-shrink-0');
  });

  it('应该在不显示模式选择器时隐藏它', () => {
    render(<Controls showModeSelector={false} />);
    
    // 模式选择器不应该存在
    expect(screen.queryByRole('combobox')).not.toBeInTheDocument();
  });

  it('应该在不显示状态栏时隐藏它', () => {
    render(<Controls showStatusBar={false} />);
    
    // 状态栏不应该存在
    expect(screen.queryByText(/模式:/)).not.toBeInTheDocument();
  });
});
